import { useDashboard } from '@/api/general';
import { getStoredAssistantData } from '@/api/general/assistantApi';
import { Beneficiary, getBeneficiariesByAssistantId } from '@/api/general/beneficiaryApi';
import { Ionicons } from '@expo/vector-icons';
import { Image } from 'expo-image';
import * as Location from 'expo-location';
import { useRouter } from 'expo-router';
import { MinusCircle, PlusCircle } from 'lucide-react-native';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import { Dimensions, Modal, Platform, SafeAreaView as RNSafeAreaView, ScrollView, StyleSheet, Switch, Text, TouchableOpacity, View } from 'react-native';
import MapView, { Marker, PROVIDER_GOOGLE } from 'react-native-maps';
import { SafeAreaView as SafeAreaContextView, useSafeAreaInsets } from 'react-native-safe-area-context';
import { WebView } from 'react-native-webview';

const { height: SCREEN_HEIGHT, width: SCREEN_WIDTH } = Dimensions.get('window');

// Interface for beneficiary visibility state
interface BeneficiaryVisibility {
  [id: string]: boolean;
}

// Interface for beneficiary selection for route
interface BeneficiarySelection {
  [id: string]: {
    selected: boolean;
    order: number;
  };
}

// Interface for beneficiary with distance
interface BeneficiaryWithDistance {
  beneficiary: Beneficiary;
  distance: number;
}

// Interface for location coordinates
interface LocationCoords {
  latitude: number;
  longitude: number;
}

// Cache for storing beneficiaries data
let mapBeneficiariesCache: Beneficiary[] | null = null;
let mapLastFetchTime: number = 0;
const MAP_CACHE_DURATION = 5 * 60 * 1000; // 5 minutes in milliseconds

// Helper function to calculate distance between two points
const calculateDistance = (lat1: number, lon1: number, lat2: number, lon2: number): number => {
  const R = 6371; // Radius of the earth in km
  const dLat = deg2rad(lat2 - lat1);
  const dLon = deg2rad(lon2 - lon1);
  const a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(deg2rad(lat1)) * Math.cos(deg2rad(lat2)) *
    Math.sin(dLon / 2) * Math.sin(dLon / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  const d = R * c; // Distance in km
  return d;
};

// Helper function to convert degrees to radians
const deg2rad = (deg: number): number => {
  return deg * (Math.PI / 180);
};

export default function MapScreen() {
  const router = useRouter();
  const insets = useSafeAreaInsets();
  const { userActionsCount } = useDashboard();
  const [lastLocation, setLastLocation] = useState<LocationCoords | null>(null);
  const [modalVisible, setModalVisible] = useState(false);
  const [routeModalVisible, setRouteModalVisible] = useState(false);
  const [beneficiaries, setBeneficiaries] = useState<Beneficiary[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isMapLoaded, setIsMapLoaded] = useState(false);
  const [showPins, setShowPins] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Track visibility state for each beneficiary
  const [beneficiaryVisibility, setBeneficiaryVisibility] = useState<BeneficiaryVisibility>({});
  // Track selection state for route planning
  const [beneficiarySelection, setBeneficiarySelection] = useState<BeneficiarySelection>({});
  const [routeWebViewUrl, setRouteWebViewUrl] = useState<string>('');
  const [routeWebViewVisible, setRouteWebViewVisible] = useState(false);
  const [selectionOrder, setSelectionOrder] = useState(0);

  const mapRef = useRef<MapView>(null);

  // Fetch beneficiaries with coordinates
  const fetchBeneficiaries = async (forceRefresh = false) => {
    try {
      // Check if we can use cached data
      const now = Date.now();
      if (!forceRefresh && mapBeneficiariesCache && (now - mapLastFetchTime) < MAP_CACHE_DURATION) {
        const beneficiariesWithCoords = mapBeneficiariesCache.filter(b => b.coordinates);
        setBeneficiaries(beneficiariesWithCoords);
        
        // Initialize visibility and selection states
        const visibilityState: BeneficiaryVisibility = {};
        const selectionState: BeneficiarySelection = {};
        beneficiariesWithCoords.forEach(beneficiary => {
          visibilityState[beneficiary.id] = true;
          selectionState[beneficiary.id] = { selected: false, order: -1 };
        });
        setBeneficiaryVisibility(visibilityState);
        setBeneficiarySelection(selectionState);
        
        setIsLoading(false);
        setIsRefreshing(false);
        // Show pins after beneficiaries are loaded
        setTimeout(() => {
          setShowPins(true);
        }, 1000);
        return;
      }

      setIsLoading(true);
      setIsRefreshing(true);
      const assistantData = await getStoredAssistantData();

      if (!assistantData || !assistantData.id) {
        setError('Assistant data not found. Please log in again.');
        return;
      }

      const beneficiariesData = await getBeneficiariesByAssistantId(assistantData.id);
      
      // Update cache
      mapBeneficiariesCache = beneficiariesData;
      mapLastFetchTime = now;
      
      // Filter only beneficiaries with coordinates
      const beneficiariesWithCoords = beneficiariesData.filter(b => b.coordinates);
      setBeneficiaries(beneficiariesWithCoords);

      // Initialize visibility and selection states
      const visibilityState: BeneficiaryVisibility = {};
      const selectionState: BeneficiarySelection = {};
      beneficiariesWithCoords.forEach(beneficiary => {
        visibilityState[beneficiary.id] = true;
        selectionState[beneficiary.id] = { selected: false, order: -1 };
      });
      setBeneficiaryVisibility(visibilityState);
      setBeneficiarySelection(selectionState);

      // Show pins after beneficiaries are loaded
      setTimeout(() => {
        setShowPins(true);
      }, 1000);

    } catch (err) {
      console.error('Error fetching beneficiaries:', err);
      setError('Failed to load beneficiaries. Please try again later.');
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  };

  useEffect(() => {
    if (isMapLoaded) {
      fetchBeneficiaries();
    }
  }, [isMapLoaded]);

  // Toggle visibility for a specific beneficiary
  const toggleBeneficiaryVisibility = (id: string) => {
    setBeneficiaryVisibility(prev => ({
      ...prev,
      [id]: !prev[id]
    }));
  };

  // Toggle selection for route planning
  const toggleBeneficiarySelection = (id: string) => {
    setBeneficiarySelection(prev => {
      const newSelection = { ...prev };
      
      // Check if we're deselecting the last item
      const isLastSelected = newSelection[id]?.selected && 
        Object.values(newSelection).filter(item => item.selected).length === 1;

      if (!newSelection[id] || !newSelection[id].selected) {
        // If selecting, check if this is the first selection after a reset
        const hasAnySelected = Object.values(newSelection).some(item => item.selected);
        
        if (!hasAnySelected) {
          // If no items are selected, start order from 1
          setSelectionOrder(1);
          newSelection[id] = {
            selected: true,
            order: 0 // Will be incremented to 1
          };
        } else {
          // If some items are already selected, continue the sequence
          newSelection[id] = {
            selected: true,
            order: selectionOrder
          };
        }
        setSelectionOrder(prev => prev + 1);
      } else {
        // If deselecting
        newSelection[id] = {
          selected: false,
          order: -1
        };
        
        // If this was the last selected item, reset the order counter
        if (isLastSelected) {
          setSelectionOrder(0);
        }
      }
      return newSelection;
    });
  };

  // Memoize sorted beneficiaries to prevent unnecessary recalculations
  const sortedBeneficiaries = useMemo(() => {
    if (!lastLocation) return [];
    
    return beneficiaries
      .filter(b => b.coordinates)
      .map(beneficiary => ({
        beneficiary,
        distance: calculateDistance(
          lastLocation.latitude,
          lastLocation.longitude,
          beneficiary.coordinates!.latitude,
          beneficiary.coordinates!.longitude
        )
      }))
      .sort((a, b) => a.distance - b.distance);
  }, [lastLocation, beneficiaries]);

  // Default center: Casablanca
  const defaultRegion = {
    latitude: 33.5731,
    longitude: -7.5898,
    latitudeDelta: 0.5,
    longitudeDelta: 0.5,
  };

  // Handle zoom in
  const handleZoomIn = () => {
    if (mapRef.current) {
      mapRef.current.getCamera().then((camera) => {
        if (camera.zoom) {
          camera.zoom += 1;
          mapRef.current?.animateCamera(camera, { duration: 300 });
        }
      }).catch(err => console.log('Error getting camera:', err));
    }
  };

  // Handle zoom out
  const handleZoomOut = () => {
    if (mapRef.current) {
      mapRef.current.getCamera().then((camera) => {
        if (camera.zoom) {
          camera.zoom -= 1;
          mapRef.current?.animateCamera(camera, { duration: 300 });
        }
      }).catch(err => console.log('Error getting camera:', err));
    }
  };

  useEffect(() => {
    (async () => {
      try {
        const { status } = await Location.requestForegroundPermissionsAsync();
        if (status === 'granted') {
          const loc = await Location.getLastKnownPositionAsync();
          if (loc && loc.coords) {
            setLastLocation({ latitude: loc.coords.latitude, longitude: loc.coords.longitude });
            console.log('Last known location:', loc.coords);
          } else {
            console.log('No last known location available');
          }
        } else {
          console.log('Location permission not granted');
        }
      } catch (e) {
        console.log('Error fetching last known location:', e);
      }
    })();
  }, []);

  // Fit all markers (places + beneficiaries + user) when locations are available
  useEffect(() => {
    if (mapRef.current && beneficiaries.length > 0) {
      const coords = beneficiaries.map(b => ({ 
        latitude: b.coordinates!.latitude, 
        longitude: b.coordinates!.longitude 
      }));

      if (lastLocation !== null) {
        coords.push({ latitude: lastLocation.latitude, longitude: lastLocation.longitude });
      }

      if (coords.length > 0) {
        mapRef.current.fitToCoordinates(coords, {
          edgePadding: { top: 60, right: 60, bottom: 60, left: 60 },
          animated: true,
        });
      }
    }
  }, [beneficiaries, lastLocation]);

  // Build directions URL for selected place
  const directionsUrl = lastLocation !== null
    ? `https://www.google.com/maps/dir/?api=1&origin=${lastLocation.latitude},${lastLocation.longitude}&travelmode=driving`
    : '';

  // Generate route URL with multiple waypoints
  const generateRouteUrl = () => {
    if (!lastLocation) return '';

    // Get selected beneficiaries and sort by selection order
    const selectedBeneficiaries = sortedBeneficiaries
      .filter(item => beneficiarySelection[item.beneficiary.id]?.selected)
      .sort((a, b) => {
        const orderA = beneficiarySelection[a.beneficiary.id]?.order || 0;
        const orderB = beneficiarySelection[b.beneficiary.id]?.order || 0;
        return orderA - orderB;
      })
      .map(item => item.beneficiary);

    if (selectedBeneficiaries.length === 0) return '';

    // For Google Maps, we need:
    // - origin (user's location)
    // - destination (last beneficiary)
    // - waypoints (all beneficiaries except the last one)

    const origin = `${lastLocation.latitude},${lastLocation.longitude}`;
    const destination = selectedBeneficiaries.length > 0
      ? `${selectedBeneficiaries[selectedBeneficiaries.length - 1].coordinates!.latitude},${selectedBeneficiaries[selectedBeneficiaries.length - 1].coordinates!.longitude}`
      : origin;

    // Waypoints are all selected beneficiaries except the last one
    const waypoints = selectedBeneficiaries.slice(0, -1)
      .map(b => `${b.coordinates!.latitude},${b.coordinates!.longitude}`)
      .join('|');

    let url = `https://www.google.com/maps/dir/?api=1&origin=${origin}&destination=${destination}&travelmode=driving`;

    // Add waypoints if there are any
    if (waypoints.length > 0) {
      url += `&waypoints=${waypoints}`;
    }

    return url;
  };

  // Start the route
  const startRoute = () => {
    const url = generateRouteUrl();
    if (url) {
      setRouteWebViewUrl(url);
      setRouteWebViewVisible(true);
      setRouteModalVisible(false);
    }
  };

  const SafeArea = Platform.OS === 'ios' ? SafeAreaContextView : RNSafeAreaView;

  return (
    <RNSafeAreaView style={styles.safeArea}>
      <View style={styles.container}>
        {/* Header */}
        <View style={styles.header}>
          <Image source={require('@/assets/images/safwa.png')} style={styles.logo} />
          <Text style={styles.headerTitle}>Map</Text>
          <View style={styles.headerIcons}>
            {routeWebViewVisible ? (
              <TouchableOpacity
                style={styles.stopRouteButton}
                onPress={() => {
                setRouteWebViewVisible(false);
                fetchBeneficiaries(true)
                }}
              >
                <Text style={styles.stopRouteButtonText}>Stop Route</Text>
              </TouchableOpacity>
            ) : (
              <>
                <TouchableOpacity style={{marginTop: 4}} onPress={() => router.push('/actions')}>
                  <Ionicons name="list-outline" size={24} color="#222" />
                  {userActionsCount > 0 && (
                    <View style={styles.badgeContainer}>
                      <Text style={styles.badgeText}>{userActionsCount}</Text>
                    </View>
                  )}
                </TouchableOpacity>
                <TouchableOpacity style={{ marginLeft: 12 }}>
                  <Ionicons name="settings-outline" size={24} color="#222" />
                </TouchableOpacity>
              </>
            )}
          </View>
        </View>

        {/* Main Content - Conditionally show Map or Route WebView */}
        {routeWebViewVisible ? (
          // Route WebView View
          <View style={[
            styles.routeWebViewContainer,
            Platform.OS === 'ios' && { paddingBottom: insets.bottom }
          ]}>
            {routeWebViewUrl && (
              <WebView
                source={{ uri: routeWebViewUrl }}
                style={styles.routeWebView}
              />
            )}
          </View>
        ) : (
          // Default Map View
          <>
            {/* Map Card at the top */}
            <View style={styles.mapCardWrapper}>
              <View style={styles.mapCard}>
                <MapView
                  ref={mapRef}
                  provider={PROVIDER_GOOGLE}
                  style={styles.map}
                  onMapReady={() => {
                    setIsMapLoaded(true);
                  }}
                >
                  {/* Beneficiary markers with opacity based on visibility state */}
                  {beneficiaries.map((beneficiary) => (
                    <Marker
                      key={`beneficiary-${beneficiary.id}`}
                      coordinate={{
                        latitude: beneficiary.coordinates!.latitude,
                        longitude: beneficiary.coordinates!.longitude
                      }}
                      title={beneficiary.name}
                      description={beneficiary.independent}
                      pinColor="#14397E"
                      opacity={showPins ? 0.5 : 0}
                      tracksViewChanges={false}
                    />
                  ))}

                  {/* User marker, if available */}
                  {lastLocation !== null && (
                    Platform.OS === 'ios' ? (
                      <Marker
                        coordinate={lastLocation}
                        title="Your Location"
                        description="This is your last known location"
                        pinColor="#EA4335"
                        opacity={showPins ? 1 : 0}
                      />
                    ) : (
                      <Marker
                        coordinate={lastLocation}
                        title="Your Location"
                        description="This is your last known location"
                        anchor={{ x: 0.5, y: 1 }}
                        opacity={showPins ? 1 : 0}
                      />
                    )
                  )}
                </MapView>

                {/* Zoom Controls */}
                <View style={styles.zoomControls}>
                  <TouchableOpacity style={styles.zoomButton} onPress={handleZoomIn}>
                    <PlusCircle size={32} color="#4AC29A" />
                  </TouchableOpacity>
                  <TouchableOpacity style={styles.zoomButton} onPress={handleZoomOut}>
                    <MinusCircle size={32} color="#4AC29A" />
                  </TouchableOpacity>
                </View>
              </View>
            </View>

            {/* Places and Beneficiaries List */}
            <ScrollView
              style={styles.placesList}
              contentContainerStyle={{
                paddingBottom: Platform.OS === 'ios' ? insets.bottom + 80 : 80
              }}
            >
              {/* Section Title for Beneficiaries */}
              <View style={styles.sectionTitleContainer}>
                <Text style={styles.sectionTitle}>Beneficiaries with Location</Text>
                <TouchableOpacity 
                  onPress={() => void fetchBeneficiaries(true)}
                  style={styles.refreshButton}
                >
                  <Ionicons name="refresh" size={24} color="#4AC29A" />
                </TouchableOpacity>
              </View>
              {isLoading ? (
                <View style={styles.loadingContainer}>
                  <Text>Loading beneficiaries...</Text>
                </View>
              ) : error ? (
                <View style={styles.errorContainer}>
                  <Text style={styles.errorText}>{error}</Text>
                </View>
              ) : sortedBeneficiaries.length === 0 ? (
                <View style={styles.emptyState}>
                  <Text style={styles.emptyStateText}>No beneficiaries with location data</Text>
                </View>
              ) : (
                sortedBeneficiaries.map(({ beneficiary, distance }) => (
                  <View key={`beneficiary-${beneficiary.id}`} style={styles.placeRow}>
                    <View style={styles.placeInfo}>
                      <Text style={styles.placeName}>{beneficiary.name}</Text>
                      <Text style={styles.placeDescription}>
                        {beneficiary.independent}
                        {lastLocation && <Text style={styles.distanceText}> • {distance.toFixed(1)} km</Text>}
                      </Text>
                    </View>
                    <View style={styles.placeActions}>
                      {/* Toggle switch for this beneficiary */}
                      <Switch
                        value={beneficiaryVisibility[beneficiary.id]}
                        onValueChange={() => toggleBeneficiaryVisibility(beneficiary.id)}
                        trackColor={{ false: '#D1D1D1', true: '#4AC29A' }}
                        thumbColor={beneficiaryVisibility[beneficiary.id] ? '#fff' : '#f4f3f4'}
                        style={styles.beneficiarySwitch}
                      />
                    </View>
                  </View>
                ))
              )}
            </ScrollView>

            {/* Start a Route Button */}
            <TouchableOpacity
              style={[
                styles.floatingButton,
                Platform.OS === 'ios' && { bottom: insets.bottom + 30 }
              ]}
              onPress={() => setRouteModalVisible(true)}
              disabled={!lastLocation || sortedBeneficiaries.length === 0}
            >
              <Ionicons name="map-outline" size={24} color="#fff" />
            </TouchableOpacity>
          </>
        )}

        {/* Modal for WebView */}
        <Modal
          visible={modalVisible}
          animationType="fade"
          transparent
          onRequestClose={() => setModalVisible(false)}
        >
          <View style={styles.modalOverlay}>
            <View style={[styles.modalContent, { paddingBottom: insets.bottom }]}>
              <View style={styles.modalTopBar}>
                <Text style={styles.modalPlaceTitle}>{directionsUrl.length > 0 && directionsUrl}</Text>

                <TouchableOpacity style={styles.modalBackButton} onPress={() => setModalVisible(false)}>
                  <Text style={styles.modalBackButtonText}>Retour</Text>
                </TouchableOpacity>
              </View>
              {directionsUrl.length > 0 && (
                <WebView source={{ uri: directionsUrl }} style={styles.webview} />
              )}
            </View>
          </View>
        </Modal>

        {/* Route Planning Modal */}
        <Modal
          visible={routeModalVisible}
          animationType="slide"
          transparent
          onRequestClose={() => setRouteModalVisible(false)}
        >
          <View style={styles.modalOverlay}>
            <View style={[styles.modalContent, { paddingBottom: insets.bottom }]}>
              <View style={styles.modalTopBar}>
                <Text style={styles.modalPlaceTitle}>Plan Your Route</Text>
              </View>

              <ScrollView
                style={styles.routeModalList}
                contentContainerStyle={{
                  paddingBottom: Platform.OS === 'ios' ? insets.bottom : 0
                }}
              >
                <Text style={styles.routeModalSubtitle}>Select beneficiaries to visit:</Text>

                {sortedBeneficiaries.map(({ beneficiary, distance }) => (
                  <TouchableOpacity
                    key={`route-${beneficiary.id}`}
                    style={styles.routeSelectionRow}
                    onPress={() => toggleBeneficiarySelection(beneficiary.id)}
                  >
                    <View style={styles.routeSelectionInfo}>
                      <Text style={styles.routeSelectionName}>{beneficiary.name}</Text>
                      <Text style={styles.routeSelectionDescription}>
                        {beneficiary.independent} • {distance.toFixed(1)} km
                      </Text>
                    </View>
                    <View style={[
                      styles.radioButton,
                      beneficiarySelection[beneficiary.id]?.selected && styles.radioButtonSelected
                    ]}>
                      {beneficiarySelection[beneficiary.id]?.selected && (
                        <View style={styles.radioButtonInner} />
                      )}
                    </View>
                  </TouchableOpacity>
                ))}
              </ScrollView>

              <TouchableOpacity
                style={styles.startButton}
                onPress={startRoute}
                disabled={!Object.values(beneficiarySelection).some(selected => selected)}
              >
                <Text style={styles.startButtonText}>Start Route</Text>
              </TouchableOpacity>
              
                <TouchableOpacity style={styles.modalBackButton} onPress={() => setRouteModalVisible(false)}>
                  <Text style={styles.modalBackButtonText}>Cancel</Text>
                </TouchableOpacity>
            </View>
          </View>
        </Modal>

        {/* Route WebView is now part of the main view */}
      </View>
    </RNSafeAreaView>
  );
}

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#fff',

  },
  container: {
    flex: 1,
    backgroundColor: '#fff',
    position: 'relative',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingBottom: 12,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  logo: {
    width: 43,
    height: 36,
    borderRadius: 18,
    marginRight: 8,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    flex: 1,
    color: '#222',
  },
  headerIcons: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  toggleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
    paddingHorizontal: 20,
    marginTop: 10,
    marginBottom: 5,
  },
  toggleLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: '#444',
    marginRight: 8,
  },
  mapCardWrapper: {
    marginTop: 8,
    alignItems: 'center',
  },
  mapCard: {
    width: '90%',
    height: SCREEN_HEIGHT * 0.3, // Slightly smaller map
    borderRadius: 24,
    overflow: 'hidden',
    backgroundColor: '#fff',
    shadowColor: '#000',
    shadowOpacity: 0.10,
    shadowRadius: 12,
    shadowOffset: { width: 0, height: 4 },
    elevation: 6,
  },
  zoomControls: {
    position: 'absolute',
    right: 16,
    bottom: 20,
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
  },
  zoomButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 8,
    shadowColor: '#000',
    shadowOpacity: 0.2,
    shadowRadius: 4,
    shadowOffset: { width: 0, height: 2 },
    elevation: 4,
  },
  map: {
    flex: 1,
    borderRadius: 24,
  },
  placesList: {
    flex: 1,
    marginTop: 18,
    paddingHorizontal: 18,
  },
  sectionTitleContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#222',
  },
  placeRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#F8FAFF',
    borderRadius: 12,
    paddingVertical: 10,
    paddingHorizontal: 14,
    marginBottom: 8,
    shadowColor: '#000',
    shadowOpacity: 0.03,
    shadowRadius: 3,
    elevation: 1,
  },
  placeInfo: {
    flex: 1,
  },
  placeName: {
    fontSize: 15,
    fontWeight: 'bold',
    color: '#222',
  },
  placeDescription: {
    fontSize: 12,
    color: '#666',
    marginTop: 2,
  },
  placeActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  beneficiarySwitch: {
    transform: [{ scaleX: 0.8 }, { scaleY: 0.8 }],
    marginRight: 4,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.75)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    width: SCREEN_WIDTH * 0.92,
    height: SCREEN_HEIGHT * 0.8,
    backgroundColor: '#fff',
    borderRadius: 24,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOpacity: 0.18,
    shadowRadius: 16,
    shadowOffset: { width: 0, height: 6 },
    elevation: 10,
  },
  modalTopBar: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingTop: 14,
    paddingBottom: 10,
    backgroundColor: '#fff',
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    zIndex: 10,
  },

  modalBackButton: {
    backgroundColor: '#4AC29A00',
    borderColor:'#ff000050',
    borderWidth:2,
    paddingVertical: 14,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 16,
    marginHorizontal: 16,
  },
  modalBackButtonText: {
    color: '#ff0000',
    fontSize: 16,
    fontWeight: 'bold',
  },
  modalPlaceTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#222',
    flex: 1,
  },
  webview: {
    flex: 1,
    backgroundColor: '#fff',
  },
  emptyState: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 30,
    backgroundColor: '#F8FAFF',
    borderRadius: 16,
    marginTop: 10,
  },
  emptyStateText: {
    fontSize: 16,
    color: '#888',
    textAlign: 'center',
  },
  distanceText: {
    fontSize: 12,
    color: '#888',
  },
  floatingButton: {
    position: 'absolute',
    right: 20,
    bottom: 20,
    width: 65,
    height: 65,
    borderRadius: 28,
    backgroundColor: '#4AC29A',
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOpacity: 0.2,
    shadowRadius: 8,
    shadowOffset: { width: 0, height: 4 },
    elevation: 5,
    zIndex: 10,
  },
  routeModalList: {
    flex: 1,
    paddingHorizontal: 16,
  },
  routeModalSubtitle: {
    fontSize: 16,
    color: '#666',
    marginBottom: 16,
    marginTop: 8,
  },
  routeSelectionRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
    paddingHorizontal: 16,
    backgroundColor: '#F8FAFF',
    borderRadius: 12,
    marginBottom: 10,
  },
  routeSelectionInfo: {
    flex: 1,
  },
  routeSelectionName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#222',
  },
  routeSelectionDescription: {
    fontSize: 13,
    color: '#666',
    marginTop: 2,
  },
  radioButton: {
    width: 22,
    height: 22,
    borderRadius: 11,
    borderWidth: 2,
    borderColor: '#4AC29A',
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: 12,
  },
  radioButtonSelected: {
    borderColor: '#4AC29A',
  },
  radioButtonInner: {
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: '#4AC29A',
  },
  startButton: {
    backgroundColor: '#4AC29A',
    paddingVertical: 14,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    margin: 16,
  },
  startButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  // Route WebView styles are now defined below
  // Route WebView styles
  routeWebViewContainer: {
    flex: 1,
    backgroundColor: '#fff',
    position: 'relative',
  },
  routeWebViewHeader: {
    backgroundColor: '#4AC29A',
    paddingVertical: 16,
    paddingHorizontal: 16,
    flexDirection: 'row',
    alignItems: 'center',
  },
  routeWebViewBackButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  routeWebViewBackText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 8,
  },
  routeWebView: {
    flex: 1,
  },
  stopRouteButton: {
    backgroundColor: '#EA4335',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  stopRouteButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: 'bold',
  },
  badgeContainer: {
    position: 'absolute',
    top: -5,
    right: -8,
    backgroundColor: '#FF5252',
    borderRadius: 10,
    width: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  badgeText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: 'bold',
  },
  loadingContainer: {
    padding: 20,
    alignItems: 'center',
  },
  errorContainer: {
    padding: 20,
    alignItems: 'center',
  },
  errorText: {
    color: '#FF6B6B',
    textAlign: 'center',
  },
  refreshButton: {
    padding: 4,
  },
  selectionOrder: {
    color: '#4AC29A',
    fontWeight: 'bold',
  },
});
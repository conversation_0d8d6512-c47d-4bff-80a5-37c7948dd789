import AsyncStorage from '@react-native-async-storage/async-storage';
import { ApiClient } from './apiClient';

const API_URL = process.env.EXPO_PUBLIC_API_URL || 'http://localhost:3000/api';

// Define types for the API responses based on the actual API response
export interface ApiBeneficiary {
  id: number;
  independent: boolean;
  fullName: string;
  phoneNumber: string | null;
  email: string | null;
  zoneName: string;
  pictureUrl: string | null;
  pictureBase64: string | null;
  coordinates: string | null;
}

// Interface for the paginated response
export interface BeneficiaryResponse {
  content: ApiBeneficiary[];
  pageable: {
    pageNumber: number;
    pageSize: number;
    sort: Array<{
      direction: string;
      property: string;
      ignoreCase: boolean;
      nullHandling: string;
      ascending: boolean;
      descending: boolean;
    }>;
    offset: number;
    unpaged: boolean;
    paged: boolean;
  };
  last: boolean;
  totalElements: number;
  totalPages: number;
  size: number;
  number: number;
  sort: Array<{
    direction: string;
    property: string;
    ignoreCase: boolean;
    nullHandling: string;
    ascending: boolean;
    descending: boolean;
  }>;
  first: boolean;
  numberOfElements: number;
  empty: boolean;
}

// Simplified Beneficiary interface for use in the app
export interface Beneficiary {
  id: string;
  name: string;
  independent: string;
  phoneNumber?: string;
  email?: string;
  coordinates?: {
    latitude: number;
    longitude: number;
  };
  pictureUrl?: string;
  pictureBase64?: string;
}

export interface Coordinates {
  latitude: number;
  longitude: number;
}

/**
 * Maps API beneficiary data to the app's Beneficiary format
 * @param apiBeneficiary The beneficiary data from the API
 * @returns Formatted beneficiary data for the app
 */
const mapApiBeneficiaryToAppFormat = (apiBeneficiary: ApiBeneficiary): Beneficiary => {
  // Parse coordinates if they exist
  apiBeneficiary.pictureBase64 = null;
  console.log(apiBeneficiary.coordinates?.split("$"));
  

  return {
    id: apiBeneficiary.id.toString(),
    name: apiBeneficiary.fullName,
    independent: apiBeneficiary.independent ? 'Indépendant' : 'Membre de famille', // Default status since API doesn't provide it
    phoneNumber: apiBeneficiary.phoneNumber || undefined,
    email: apiBeneficiary.email || undefined,
    pictureUrl: apiBeneficiary.pictureUrl || undefined,
    pictureBase64: apiBeneficiary.pictureBase64 || undefined,
    coordinates: apiBeneficiary.coordinates ? {
      latitude: parseFloat(apiBeneficiary.coordinates.split('$')[0]),
      longitude: parseFloat(apiBeneficiary.coordinates.split('$')[1])
    } : undefined
  };
};

/**
 * Fetches beneficiaries by assistant ID
 * @param assistantId The ID of the assistant
 * @returns The beneficiaries data or empty array if not found
 */
export const getBeneficiariesByAssistantId = async (assistantId: number): Promise<Beneficiary[]> => {
  try {
    // Use the ApiClient to make the request
    const response = await ApiClient.get<BeneficiaryResponse>(`/mobile/assistant/beneficiary/by-assistant/${assistantId}`);

    // Map the API response to our app's format
    if (response && response.content && Array.isArray(response.content)) {
      return response.content.map(mapApiBeneficiaryToAppFormat);
    }

    return [];
  } catch (error) {
    console.error('Error fetching beneficiaries data:', error);
    return [];
  }
};

/**
 * Stores a beneficiary in AsyncStorage for later retrieval
 * @param beneficiary The beneficiary to store
 */
export const storeBeneficiary = async (beneficiary: Beneficiary): Promise<void> => {
  try {
    await AsyncStorage.setItem(`beneficiary_${beneficiary.id}`, JSON.stringify(beneficiary));
  } catch (error) {
    console.error('Error storing beneficiary:', error);
  }
};

/**
 * Retrieves a beneficiary from AsyncStorage by ID
 * @param id The ID of the beneficiary to retrieve
 * @returns The beneficiary or null if not found
 */
export const getBeneficiaryById = async (id: string): Promise<Beneficiary | null> => {
  try {
    const data = await AsyncStorage.getItem(`beneficiary_${id}`);
    if (data) {
      return JSON.parse(data) as Beneficiary;
    }
    return null;
  } catch (error) {
    console.error('Error retrieving beneficiary:', error);
    return null;
  }
};

export const saveBeneficiaryCoordinates = async (beneficiaryId: string, coordinates: Coordinates): Promise<void> => {
  try {
    // Format coordinates as "latitude$longitude"
    const coordinatesString = `${coordinates.latitude}$${coordinates.longitude}`;
    
    // Use ApiClient with the correct endpoint
    await ApiClient.post(`/beneficiaries/${beneficiaryId}/coordinates`, {
      coordinates: coordinatesString
    }).catch(error => {
      // If the error is about JSON parsing but the request was successful (status 200-299)
      if (error instanceof SyntaxError && error.message.includes('JSON')) {
        // The coordinates were saved successfully, we can ignore this error
        return;
      }
      // For any other error, rethrow it
      throw error;
    });

  } catch (error) {
    console.error('Error saving coordinates:', error);
    if (error instanceof Error) {
      throw new Error(`Failed to save coordinates: ${error.message}`);
    }
    throw new Error('Failed to save coordinates: Unknown error');
  }
};
